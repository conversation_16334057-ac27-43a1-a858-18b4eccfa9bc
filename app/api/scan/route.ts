import { NextRequest, NextResponse } from 'next/server'
import { SecurityScanner, parseGitHubUrl, parseGitHubUser } from '@/lib/security-scanner'
import { RoastGenerator } from '@/lib/roast-generator'

export async function POST(request: NextRequest) {
  try {
    const { input, scanType = 'user' } = await request.json()

    if (!input) {
      return NextResponse.json(
        { error: 'GitHub username or repository URL is required' },
        { status: 400 }
      )
    }

    // Initialize scanner and roast generator
    const scanner = new SecurityScanner()
    const roastGenerator = new RoastGenerator()

    try {
      if (scanType === 'user') {
        // Parse GitHub username
        const username = parseGitHubUser(input)
        if (!username) {
          return NextResponse.json(
            { error: 'Invalid GitHub username. Please provide a valid GitHub username or profile URL.' },
            { status: 400 }
          )
        }

        // Scan the user profile
        const userScanResult = await scanner.scanUser(username)

        // Generate roast based on findings
        const roast = roastGenerator.generateUserRoast(userScanResult)

        return NextResponse.json(roast)
      } else {
        // Legacy repository scanning (keeping for backward compatibility)
        const parsed = parseGitHubUrl(input)
        if (!parsed) {
          return NextResponse.json(
            { error: 'Invalid GitHub URL. Please provide a valid GitHub repository URL.' },
            { status: 400 }
          )
        }

        const { owner, repo } = parsed

        // Scan the repository
        const scanResult = await scanner.scanRepository(owner, repo)

        // Generate roast based on findings
        const roast = roastGenerator.generateRepoRoast(scanResult, input)

        return NextResponse.json(roast)
      }
    } catch (scanError: any) {
      console.error('Scan error:', scanError)

      // Handle specific GitHub API errors
      if (scanError.status === 404) {
        return NextResponse.json(
          { error: scanType === 'user'
            ? 'User not found. Make sure the username exists on GitHub.'
            : 'Repository not found. Make sure the repository exists and is public.' },
          { status: 404 }
        )
      }

      if (scanError.status === 403) {
        return NextResponse.json(
          { error: 'Access denied. You might have hit the rate limit or the resource is private.' },
          { status: 403 }
        )
      }

      return NextResponse.json(
        { error: scanError.message || 'Failed to scan' },
        { status: 500 }
      )
    }
  } catch (error: any) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
