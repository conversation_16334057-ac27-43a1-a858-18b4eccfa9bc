"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Zap } from "lucide-react"

export default function DumbDevScanner() {
  const [userInput, setUserInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleScan = async () => {
    if (!userInput.trim()) {
      setError("Please enter a GitHub username");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock result for now
      setResult({
        username: userInput,
        score: Math.floor(Math.random() * 100),
        message: "This is a mock result. The actual scanning functionality will be implemented next."
      });
    } catch (err) {
      setError("Failed to scan profile. Please try again.");
      console.error("Scan error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const resetScan = () => {
    setUserInput("");
    setResult(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
            DumbDev Scanner
          </h1>
          <p className="text-lg text-slate-300 max-w-2xl mx-auto">
            Enter a GitHub username to analyze code security and get a roast of your coding practices.
          </p>
        </div>

        {!result ? (
          <Card className="max-w-2xl mx-auto bg-slate-800/80 border border-slate-700/50">
            <CardHeader className="text-center pt-8 pb-6">
              <div className="w-20 h-20 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-cyan-500/10 to-purple-500/10 flex items-center justify-center border border-cyan-500/20">
                <Zap className="w-10 h-10 text-cyan-400" strokeWidth={1.5} />
              </div>
              <CardTitle className="text-2xl font-bold text-slate-100">
                Scan a GitHub Profile
              </CardTitle>
              <CardDescription className="text-slate-400 mt-2">
                Enter a GitHub username or profile URL to analyze code security
              </CardDescription>
            </CardHeader>
            <CardContent className="pb-8 px-8">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Input
                    placeholder="username or github.com/username"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleScan()}
                    className="h-12 bg-slate-800/50 border-slate-700/50 text-white placeholder-slate-500 focus:ring-2 focus:ring-cyan-500/50 focus:border-transparent"
                    disabled={isLoading}
                  />
                  {error && (
                    <p className="text-sm text-red-400">{error}</p>
                  )}
                </div>
                <Button
                  onClick={handleScan}
                  disabled={isLoading || !userInput.trim()}
                  className="w-full h-12 text-base font-medium bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 transition-all"
                >
                  {isLoading ? 'Scanning...' : 'Scan Profile'}
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="max-w-4xl mx-auto bg-slate-800/80 border border-slate-700/50">
            <div className="h-1.5 bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500"></div>
            <CardHeader className="text-center pt-10 pb-8">
              <h2 className="text-3xl font-bold text-white mb-2">
                Scan Results for {result.username}
              </h2>
              <p className="text-lg text-slate-300">
                {result.message}
              </p>
              <div className="mt-6">
                <div className="text-5xl font-bold text-cyan-400">
                  {result.score}/100
                </div>
                <div className="text-sm text-slate-400 mt-2">SECURITY SCORE</div>
              </div>
            </CardHeader>
            <CardContent className="pb-10 px-8">
              <div className="mt-8 pt-6 border-t border-slate-700/50">
                <Button
                  onClick={resetScan}
                  variant="outline"
                  className="w-full h-12 text-base font-medium border-slate-700/50 text-slate-200 hover:bg-slate-700/30 hover:border-slate-600/50"
                >
                  Scan Another Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white overflow-hidden relative">
      {/* Modern gradient background */}
      <div className="fixed inset-0 -z-10 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
        {/* Subtle grid pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(#2a2a40_1px,transparent_1px)] [background-size:16px_16px] opacity-10"></div>
        
        {/* Animated gradient blobs */}
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-cyan-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-blob"></div>
        <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-purple-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-pink-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-3 mb-6 px-6 py-2 rounded-full bg-slate-800/50 border border-slate-700/50">
            <span className="text-cyan-400">🚀</span>
            <span className="text-sm font-medium text-slate-300">ROASTING DEVS SINCE 2024</span>
          </div>
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
            Dumb Dev Scanner
          </h1>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto leading-relaxed">
            Enter a GitHub username to analyze code security and get a <span className="font-medium text-cyan-300">hilarious roast</span> of your coding practices.
            We scan for security issues, code quality, and more!
          </p>
        </div>

        {!showResult ? (
          <Card className="max-w-2xl mx-auto bg-slate-800/80 border border-slate-700/50 shadow-2xl shadow-slate-900/50 backdrop-blur-sm overflow-hidden">
            <div className="h-1.5 bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500"></div>
            <CardHeader className="text-center pt-8 pb-6">
              <div className="w-20 h-20 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-cyan-500/10 to-purple-500/10 flex items-center justify-center border border-cyan-500/20">
                <Zap className="w-10 h-10 text-cyan-400" strokeWidth={1.5} />
              </div>
              <CardTitle className="text-2xl font-bold text-slate-100">
                Scan a GitHub Profile
              </CardTitle>
              <CardDescription className="text-slate-400 mt-2">
                Enter a GitHub username or profile URL to analyze code security
              </CardDescription>
              <CardDescription className="text-xl text-slate-300">
                Drop that GitHub username and prepare to get{" "}
                <span className="text-pink-300 font-bold">ABSOLUTELY ROASTED!</span> 🔥💀
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4 mt-2 px-6 pb-8">
                {isScanning ? (
                  <div className="space-y-6 py-6">
                    <div className="text-center space-y-4">
                      <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-cyan-500/10 to-purple-500/10 flex items-center justify-center border border-cyan-500/20 mb-4">
                        <Zap className="w-8 h-8 text-cyan-400 animate-pulse" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-lg font-medium text-slate-200">{currentMessage}</h3>
                        <p className="text-sm text-slate-400">This might take a moment...</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm text-slate-500">
                        <span>Scanning progress</span>
                        <span>{Math.round(scanProgress)}%</span>
                      </div>
                      <Progress value={scanProgress} className="h-2 bg-slate-800/50" />
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="space-y-2">
                      <div className="relative">
                        <Github className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
                        <Input
                          type="text"
                          placeholder="username or github.com/username"
                          value={userInput}
                          onChange={(e) => setUserInput(e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && startScan()}
                          className="pl-10 h-12 bg-slate-800/50 border-slate-700/50 text-white placeholder-slate-500 focus:ring-2 focus:ring-cyan-500/50 focus:border-transparent"
                        />
                      </div>
                      {error && (
                        <p className="text-sm text-red-400 flex items-center">
                          <AlertTriangle className="w-4 h-4 mr-1.5" />
                          {error}
                        </p>
                      )}
                    </div>
                    
                    <div className="space-y-3">
                      <Button
                        onClick={startScan}
                        disabled={isScanning}
                        size="lg"
                        className="w-full h-12 text-base font-medium bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 transition-all transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
                      >
                        <Zap className="w-4 h-4 mr-2" />
                        Scan Profile
                      </Button>
                      
                      <div className="flex items-center justify-center text-xs text-slate-500">
                        <span className="inline-flex items-center">
                          <svg className="w-3.5 h-3.5 mr-1.5 text-amber-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h2a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                          </svg>
                          We only scan public repositories
                        </span>
                      </div>
                    </div>
                  </> 
                )}
              </div>

              {isScanning && (
                <div className="text-center text-slate-400">
                  <div className="animate-spin text-4xl mb-2">🔍</div>
                  <p>Our AI is judging your life choices... 🤖⚖️💀</p>
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card className="max-w-4xl mx-auto bg-slate-800/80 border border-slate-700/50 shadow-2xl shadow-slate-900/50 backdrop-blur-sm overflow-hidden">
            <div className="h-1.5 bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500"></div>
            <CardHeader className="text-center pt-10 pb-8 px-8">
              <div className="flex flex-col md:flex-row items-center justify-between">
                <div className="text-center md:text-left mb-8 md:mb-0">
                  <div className="inline-flex items-center bg-slate-700/50 px-4 py-1.5 rounded-full text-sm font-medium text-cyan-300 mb-3 border border-cyan-500/30">
                    <span className="w-2 h-2 rounded-full bg-cyan-400 mr-2 animate-pulse"></span>
                    Scan Complete
                  </div>
                  <h2 className="text-3xl font-bold text-white mb-2">
                    {result.title}
                  </h2>
                  <p className="text-lg text-slate-300 max-w-2xl">{result.comment}</p>
                </div>
                <div className="relative">
                  <DumbDevScore score={result.averageScore} size={140} />
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <span className="text-4xl font-bold text-white">{result.averageScore}</span>
                    <span className="text-sm text-slate-400 mt-1">SECURITY SCORE</span>
                  </div>
                </div>
              </div>
                  {result.bestRepo && (
                    <div className="mt-8 p-5 bg-green-900/10 rounded-lg border border-green-500/20">
                      <div className="flex items-center mb-3">
                        <div className="w-1.5 h-6 bg-green-500 rounded-full mr-3"></div>
                        <h4 className="text-lg font-semibold text-green-300 flex items-center">
                          <span className="mr-2">🏆</span> Best Repository
                        </h4>
                      </div>
                      <div className="pl-5">
                        <div className="text-white font-medium">{result.bestRepo.name}</div>
                        <div className="flex items-center mt-2 text-sm">
                          <div className="px-2 py-0.5 bg-green-500/20 text-green-300 rounded-full mr-2">
                            Score: {result.bestRepo.score}/100
                          </div>
                          <div className="text-slate-400">
                            {result.bestRepo.issues.length} issues • {result.bestRepo.typoScan.totalTypos} typos
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Specific Findings */}
              {result.specificFindings && result.specificFindings.length > 0 && (
                <div className="bg-slate-800/50 p-6 rounded-lg border border-slate-700/50">
                  <div className="flex items-center mb-5">
                    <div className="w-1.5 h-6 bg-amber-500 rounded-full mr-3"></div>
                    <h3 className="text-xl font-semibold text-amber-300">
                      Key Findings
                    </h3>
                    <span className="ml-3 px-2.5 py-0.5 text-xs font-medium bg-amber-500/20 text-amber-300 rounded-full">
                      {result.specificFindings.length} items
                    </span>
                  </div>
                  <div className="space-y-3">
                    {result.specificFindings.map((finding: string, index: number) => (
                      <div 
                        key={index} 
                        className="flex items-start p-4 bg-slate-700/30 rounded-lg border border-slate-700 hover:border-amber-500/30 transition-colors group"
                      >
                        <div className="flex-shrink-0 mt-0.5">
                          <div className="w-5 h-5 rounded-full bg-amber-500/20 flex items-center justify-center">
                            <svg className="w-3 h-3 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                        </div>
                        <p className="ml-3 text-slate-200">{finding}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Security Issues Details */}
              {result.repositories && result.repositories.some((repo: any) => repo.issues.length > 0) && (
                <div className="bg-slate-800/50 p-6 rounded-lg border border-slate-700/50">
                  <div className="flex items-center mb-5">
                    <div className="w-1.5 h-6 bg-rose-500 rounded-full mr-3"></div>
                    <h3 className="text-xl font-semibold text-rose-300">
                      Security Issues
                    </h3>
                    <span className="ml-3 px-2.5 py-0.5 text-xs font-medium bg-rose-500/20 text-rose-300 rounded-full">
                      {result.totalIssues} issues found
                    </span>
                  </div>
                  <div className="space-y-4">
                    {result.repositories.flatMap((repo: any) =>
                      repo.issues.map((issue: any, issueIndex: number) => ({
                        ...issue,
                        repoName: repo.name,
                        key: `${repo.name}-${issueIndex}`
                      }))
                    ).slice(0, 5).map((issue: any) => (
                      <div 
                        key={issue.key} 
                        className="p-4 bg-slate-700/30 rounded-lg border border-slate-700 hover:border-rose-500/30 transition-colors group"
                      >
                        <div className="flex flex-wrap items-center gap-2 mb-3">
                          <Badge 
                            variant={issue.severity === 'critical' ? 'destructive' : 'secondary'} 
                            className="text-xs font-medium px-2 py-0.5"
                          >
                            {issue.severity === 'critical' && '🚨 '}
                            {issue.severity === 'high' && '⚠️ '}
                            {issue.severity === 'medium' && 'ℹ️ '}
                            {issue.severity === 'low' && '🔍 '}
                            {issue.severity.toUpperCase()}
                          </Badge>
                          <Badge variant="outline" className="text-xs text-slate-300 bg-slate-700/50 border-slate-600/50">
                            {issue.type}
                          </Badge>
                          <Badge variant="outline" className="text-xs text-purple-300 bg-slate-800/50 border-purple-500/30">
                            {issue.repoName}
                          </Badge>
                        </div>
                        <h4 className="font-semibold text-slate-100 mb-1.5">{issue.title}</h4>
                        <p className="text-sm text-slate-300 mb-3">{issue.description}</p>
                        <div className="p-3 bg-slate-800/50 rounded-md border border-slate-700/50">
                          <div className="flex items-start">
                            <svg className="w-4 h-4 text-cyan-400 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p className="text-sm text-cyan-300">{issue.suggestion}</p>
                          </div>
                        </div>
                        {issue.file && (
                          <div className="mt-3 flex items-center text-xs text-slate-400">
                            <svg className="w-3.5 h-3.5 mr-1.5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            {issue.file}
                          </div>
                        )}
                      </div>
                    ))}
                    {result.totalIssues > 5 && (
                      <div className="text-center">
                        <p className="inline-flex items-center text-sm text-slate-400 bg-slate-800/50 px-4 py-2 rounded-full border border-slate-700/50">
                          <svg className="w-4 h-4 mr-1.5 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                          Show {result.totalIssues - 5} more issues
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="mt-10 pt-6 border-t border-slate-700/50">
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    onClick={shareResult}
                    size="lg"
                    className="h-12 px-6 text-base font-medium bg-gradient-to-r from-cyan-600 to-purple-600 hover:from-cyan-600/90 hover:to-purple-600/90 transition-all duration-200"
                  >
                    <Share2 className="mr-2 h-4 w-4" />
                    Share Results
                  </Button>
                  <Button
                    onClick={resetScan}
                    variant="outline"
                    size="lg"
                    className="h-12 px-6 text-base font-medium border-slate-700/50 text-slate-200 hover:bg-slate-700/30 hover:border-slate-600/50 transition-all duration-200"
                  >
                    <RotateCcw className="mr-2 h-4 w-4" />
                    Scan Another Profile
                  </Button>
                </div>
              </div>

              {/* Disclaimer */}
              <div className="mt-8 pt-6 border-t border-slate-700/50">
                <div className="bg-slate-800/30 p-4 rounded-lg border border-slate-700/50">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-0.5">
                      <div className="w-5 h-5 rounded-full bg-amber-500/20 flex items-center justify-center">
                        <svg className="w-3 h-3 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-amber-300">Important Notice</h4>
                      <p className="text-sm text-slate-400 mt-1">
                        This tool scans public GitHub profiles and repositories for educational purposes only. 
                        No data is stored, and we only access publicly available information. 
                        Please use responsibly and always follow security best practices.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Footer */}
        <div className="text-center mt-12 text-slate-400">
          <p className="text-lg font-bold">Made with 💀 and lots of caffeine by developers who've been there 😭</p>
          <p className="text-sm mt-2">Remember: We roast because we care! Now go fix those security issues! 🛠️💀</p>
        </div>
      </div>
    </div>
  )
}
