export interface TypoIssue {
  type: 'repo_name' | 'description' | 'readme' | 'commit_message' | 'variable_name'
  location: string
  original: string
  suggestion?: string
  severity: 'high' | 'medium' | 'low'
  context?: string
}

export interface TypoScanResult {
  totalTypos: number
  typosByType: Record<string, number>
  issues: TypoIssue[]
  embarrassmentLevel: 'cringe' | 'awkward' | 'noticeable' | 'minor' | 'clean'
}

export class TypoDetector {
  // Common programming typos and their corrections
  private commonTypos = new Map([
    // Programming terms
    ['lenght', 'length'],
    ['widht', 'width'],
    ['heigth', 'height'],
    ['recieve', 'receive'],
    ['seperate', 'separate'],
    ['definately', 'definitely'],
    ['occured', 'occurred'],
    ['begining', 'beginning'],
    ['sucessful', 'successful'],
    ['accesible', 'accessible'],
    ['responsable', 'responsible'],
    ['availible', 'available'],
    ['visibile', 'visible'],
    ['compatability', 'compatibility'],
    ['dependancy', 'dependency'],
    ['independant', 'independent'],
    ['persistant', 'persistent'],
    ['consistant', 'consistent'],
    ['existant', 'existent'],
    ['resistent', 'resistant'],
    ['efficent', 'efficient'],
    ['sufficent', 'sufficient'],
    ['neccessary', 'necessary'],
    ['occassion', 'occasion'],
    ['accomodate', 'accommodate'],
    ['recomend', 'recommend'],
    ['commited', 'committed'],
    ['submited', 'submitted'],
    ['admited', 'admitted'],
    ['permited', 'permitted'],
    ['omited', 'omitted'],
    ['prefered', 'preferred'],
    ['transfered', 'transferred'],
    ['refered', 'referred'],
    ['occuring', 'occurring'],
    ['begining', 'beginning'],
    ['runing', 'running'],
    ['stoping', 'stopping'],
    ['geting', 'getting'],
    ['seting', 'setting'],
    ['formating', 'formatting'],
    ['submiting', 'submitting'],
    ['comiting', 'committing'],
    
    // Tech terms
    ['databse', 'database'],
    ['databas', 'database'],
    ['authetication', 'authentication'],
    ['authentification', 'authentication'],
    ['autorization', 'authorization'],
    ['authorisation', 'authorization'],
    ['configuraton', 'configuration'],
    ['configuartion', 'configuration'],
    ['initalization', 'initialization'],
    ['initialisation', 'initialization'],
    ['syncronization', 'synchronization'],
    ['syncronisation', 'synchronization'],
    ['optimisation', 'optimization'],
    ['serialisation', 'serialization'],
    ['deserialization', 'deserialization'],
    ['implmentation', 'implementation'],
    ['implemention', 'implementation'],
    ['documention', 'documentation'],
    ['documentaion', 'documentation'],
    ['enviroment', 'environment'],
    ['enviornment', 'environment'],
    ['developement', 'development'],
    ['develoment', 'development'],
    ['managment', 'management'],
    ['mangement', 'management'],
    ['requirment', 'requirement'],
    ['requirments', 'requirements'],
    ['performace', 'performance'],
    ['performence', 'performance'],
    ['maintenace', 'maintenance'],
    ['maintainance', 'maintenance'],
    ['compatiblity', 'compatibility'],
    ['compatibilty', 'compatibility'],
    ['responsability', 'responsibility'],
    ['responsibilty', 'responsibility'],
    
    // Common words
    ['teh', 'the'],
    ['adn', 'and'],
    ['nad', 'and'],
    ['taht', 'that'],
    ['thier', 'their'],
    ['ther', 'their'],
    ['youre', "you're"],
    ['your', "you're"], // context dependent
    ['its', "it's"], // context dependent
    ['loose', 'lose'], // context dependent
    ['affect', 'effect'], // context dependent
    ['then', 'than'], // context dependent
    ['alot', 'a lot'],
    ['aswell', 'as well'],
    ['incase', 'in case'],
    ['atleast', 'at least'],
    ['eachother', 'each other'],
    ['everytime', 'every time'],
    ['sometime', 'sometimes'], // context dependent
    ['anyways', 'anyway'],
    ['alright', 'all right'],
    ['cannot', 'can not'], // both are correct
    ['setup', 'set up'], // context dependent
    ['login', 'log in'], // context dependent
    ['backup', 'back up'], // context dependent
    ['checkout', 'check out'], // context dependent
  ])

  // Common variable naming typos
  private variableTypos = new Map([
    ['lenght', 'length'],
    ['widht', 'width'],
    ['heigth', 'height'],
    ['usr', 'user'],
    ['usrname', 'username'],
    ['passwd', 'password'],
    ['pswd', 'password'],
    ['btn', 'button'],
    ['img', 'image'],
    ['pic', 'picture'],
    ['num', 'number'],
    ['str', 'string'],
    ['obj', 'object'],
    ['arr', 'array'],
    ['elem', 'element'],
    ['el', 'element'],
    ['idx', 'index'],
    ['cnt', 'count'],
    ['tmp', 'temporary'],
    ['temp', 'temporary'],
    ['prev', 'previous'],
    ['curr', 'current'],
    ['calc', 'calculate'],
    ['init', 'initialize'],
    ['config', 'configuration'],
    ['auth', 'authentication'],
    ['admin', 'administrator'],
    ['mgr', 'manager'],
    ['ctrl', 'controller'],
    ['svc', 'service'],
    ['repo', 'repository'],
    ['db', 'database'],
    ['conn', 'connection'],
    ['req', 'request'],
    ['res', 'response'],
    ['err', 'error'],
    ['msg', 'message'],
    ['info', 'information'],
    ['desc', 'description'],
    ['val', 'value'],
    ['param', 'parameter'],
    ['arg', 'argument'],
    ['func', 'function'],
    ['proc', 'process'],
    ['exec', 'execute'],
    ['impl', 'implementation'],
    ['spec', 'specification'],
    ['ref', 'reference'],
    ['doc', 'document'],
    ['lib', 'library'],
    ['util', 'utility'],
    ['helper', 'helper'], // not really a typo but could be more descriptive
  ])

  // Words that are often misspelled in tech contexts
  private techTerms = [
    'algorithm', 'asynchronous', 'authentication', 'authorization', 'compatibility',
    'configuration', 'database', 'development', 'documentation', 'environment',
    'implementation', 'initialization', 'maintenance', 'optimization', 'performance',
    'repository', 'synchronization', 'architecture', 'infrastructure', 'deployment',
    'integration', 'migration', 'validation', 'verification', 'accessibility',
    'scalability', 'reliability', 'availability', 'security', 'vulnerability'
  ]

  scanForTypos(repoName: string, description: string, readmeContent?: string, commitMessages?: string[]): TypoScanResult {
    const issues: TypoIssue[] = []

    // Scan repository name
    this.scanText(repoName, 'repo_name', 'Repository name', issues)

    // Scan description
    if (description) {
      this.scanText(description, 'description', 'Repository description', issues)
    }

    // Scan README content
    if (readmeContent) {
      this.scanText(readmeContent, 'readme', 'README file', issues, 500) // Limit to first 500 chars
    }

    // Scan commit messages
    if (commitMessages && commitMessages.length > 0) {
      commitMessages.slice(0, 10).forEach((message, index) => {
        this.scanText(message, 'commit_message', `Commit message #${index + 1}`, issues)
      })
    }

    // Calculate statistics
    const typosByType = issues.reduce((acc, issue) => {
      acc[issue.type] = (acc[issue.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const embarrassmentLevel = this.calculateEmbarrassmentLevel(issues.length)

    return {
      totalTypos: issues.length,
      typosByType,
      issues: issues.slice(0, 20), // Limit to top 20 issues
      embarrassmentLevel
    }
  }

  private scanText(text: string, type: TypoIssue['type'], location: string, issues: TypoIssue[], maxLength?: number): void {
    if (!text) return

    const textToScan = maxLength ? text.substring(0, maxLength) : text
    const words = textToScan.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)

    words.forEach(word => {
      // Check common typos
      if (this.commonTypos.has(word)) {
        issues.push({
          type,
          location,
          original: word,
          suggestion: this.commonTypos.get(word),
          severity: this.getTypoSeverity(word, type),
          context: this.getContext(textToScan, word)
        })
      }

      // Check variable naming (for repo names and code-like text)
      if ((type === 'repo_name' || type === 'readme') && this.variableTypos.has(word)) {
        issues.push({
          type: 'variable_name',
          location,
          original: word,
          suggestion: this.variableTypos.get(word),
          severity: 'low',
          context: 'Consider using more descriptive variable names'
        })
      }

      // Check for potential misspellings of tech terms
      this.checkTechTermMisspelling(word, type, location, issues)
    })
  }

  private checkTechTermMisspelling(word: string, type: TypoIssue['type'], location: string, issues: TypoIssue[]): void {
    // Simple fuzzy matching for tech terms
    for (const techTerm of this.techTerms) {
      if (this.isLikelyMisspelling(word, techTerm)) {
        issues.push({
          type,
          location,
          original: word,
          suggestion: techTerm,
          severity: 'medium',
          context: `Possible misspelling of "${techTerm}"`
        })
        break // Only suggest one correction per word
      }
    }
  }

  private isLikelyMisspelling(word: string, target: string): boolean {
    if (word === target) return false
    if (Math.abs(word.length - target.length) > 2) return false
    
    // Simple edit distance check
    const distance = this.levenshteinDistance(word, target)
    return distance <= 2 && distance > 0 && word.length > 4
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        )
      }
    }

    return matrix[str2.length][str1.length]
  }

  private getTypoSeverity(word: string, type: TypoIssue['type']): 'high' | 'medium' | 'low' {
    // Repository names and descriptions are more visible
    if (type === 'repo_name' || type === 'description') return 'high'
    
    // README files are also quite visible
    if (type === 'readme') return 'medium'
    
    // Commit messages and variable names are less visible
    return 'low'
  }

  private getContext(text: string, word: string): string {
    const index = text.toLowerCase().indexOf(word.toLowerCase())
    if (index === -1) return ''
    
    const start = Math.max(0, index - 20)
    const end = Math.min(text.length, index + word.length + 20)
    return '...' + text.substring(start, end) + '...'
  }

  private calculateEmbarrassmentLevel(typoCount: number): TypoScanResult['embarrassmentLevel'] {
    if (typoCount === 0) return 'clean'
    if (typoCount <= 2) return 'minor'
    if (typoCount <= 5) return 'noticeable'
    if (typoCount <= 10) return 'awkward'
    return 'cringe'
  }
}
