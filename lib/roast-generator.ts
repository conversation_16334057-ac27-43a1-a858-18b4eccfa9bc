import { SecurityIssue, UserScanResult, RepoScanResult } from './security-scanner'

export interface RoastResult {
  score: number
  title: string
  comment: string
  meme: string
  emoji: string
  issues: SecurityIssue[]
  repoName: string
  repoUrl: string
  specificFindings: string[]
}

export interface UserRoastResult {
  username: string
  totalScore: number
  averageScore: number
  title: string
  comment: string
  meme: string
  emoji: string
  totalIssues: number
  totalTypos: number
  repositories: RepoScanResult[]
  userInfo: UserScanResult['userInfo']
  worstRepo: RepoScanResult | null
  bestRepo: RepoScanResult | null
  specificFindings: string[]
  profileInsults: string[]
  typoRoasts: string[]
}

export class RoastGenerator {
  generateRepoRoast(scanResult: RepoScanResult, repoUrl: string): RoastResult {
    const { score, issues, repoInfo } = scanResult
    const specificFindings = this.generateSpecificFindings(issues)

    // Generate roast based on score and specific issues
    const roastData = this.getRoastByScore(score, issues)

    return {
      score,
      title: roastData.title,
      comment: this.personalizeComment(roastData.comment, issues, repoInfo),
      meme: roastData.meme,
      emoji: roastData.emoji,
      issues,
      repoName: repoInfo.name,
      repoUrl,
      specificFindings,
    }
  }

  generateUserRoast(userScanResult: UserScanResult): UserRoastResult {
    const {
      username,
      totalScore,
      averageScore,
      totalIssues,
      totalTypos,
      repositories,
      userInfo,
      worstRepo,
      bestRepo,
      typoStats
    } = userScanResult

    // Generate specific findings across all repositories
    const allIssues = repositories.flatMap(repo => repo.issues)
    const specificFindings = this.generateUserSpecificFindings(repositories, userInfo)
    const profileInsults = this.generateProfileInsults(userInfo, repositories)
    const typoRoasts = this.generateTypoRoasts(repositories, typoStats)

    // Use average score for roast level, but consider total issues for severity
    const roastData = this.getUserRoastByScore(averageScore, totalIssues, repositories.length)

    return {
      username,
      totalScore,
      averageScore,
      title: roastData.title,
      comment: this.personalizeUserComment(roastData.comment, userInfo, repositories, worstRepo),
      meme: roastData.meme,
      emoji: roastData.emoji,
      totalIssues,
      totalTypos,
      repositories,
      userInfo,
      worstRepo,
      bestRepo,
      specificFindings,
      profileInsults,
      typoRoasts,
    }
  }

  private generateSpecificFindings(issues: SecurityIssue[]): string[] {
    const findings: string[] = []
    
    // Group issues by type for better roasting
    const secretIssues = issues.filter(i => i.type === 'secret')
    const configIssues = issues.filter(i => i.type === 'config')
    const dependencyIssues = issues.filter(i => i.type === 'dependency')
    const codeIssues = issues.filter(i => i.type === 'code')

    if (secretIssues.length > 0) {
      findings.push(`🔑 Found ${secretIssues.length} potential secret exposure(s) - your API keys are having a public party!`)
    }

    if (configIssues.length > 0) {
      const envFiles = configIssues.filter(i => i.title.includes('Environment File'))
      if (envFiles.length > 0) {
        findings.push(`📁 You committed ${envFiles.length} environment file(s) - might as well post your passwords on Twitter!`)
      }
      
      const gitignoreIssue = configIssues.find(i => i.title.includes('.gitignore'))
      if (gitignoreIssue) {
        findings.push(`🙈 No .gitignore file - you're committing EVERYTHING like it's 2005!`)
      }
    }

    if (dependencyIssues.length > 0) {
      findings.push(`📦 ${dependencyIssues.length} dependency issue(s) found - your packages are more outdated than flip phones!`)
    }

    if (codeIssues.length > 0) {
      const consoleIssues = codeIssues.filter(i => i.title.includes('Console.log'))
      const todoIssues = codeIssues.filter(i => i.title.includes('TODO'))
      const evalIssues = codeIssues.filter(i => i.title.includes('eval()'))

      if (consoleIssues.length > 0) {
        findings.push(`🖨️ Console.log statements everywhere - debugging in production like a true rebel!`)
      }
      
      if (todoIssues.length > 0) {
        findings.push(`📝 TODO comments galore - your code is more wishful thinking than actual implementation!`)
      }
      
      if (evalIssues.length > 0) {
        findings.push(`⚠️ Using eval() - because who needs security when you can execute random strings!`)
      }
    }

    return findings
  }

  private generateUserSpecificFindings(repositories: RepoScanResult[], userInfo: UserScanResult['userInfo']): string[] {
    const findings: string[] = []

    if (repositories.length === 0) {
      findings.push(`🏜️ Empty GitHub profile - are you sure you're a developer? 🤔`)
      return findings
    }

    const totalIssues = repositories.reduce((sum, repo) => sum + repo.issues.length, 0)
    const avgScore = repositories.reduce((sum, repo) => sum + repo.score, 0) / repositories.length

    // Overall statistics roasts
    if (totalIssues > 50) {
      findings.push(`🚨 ${totalIssues} total security issues across your repos - you're a walking vulnerability database! 💀`)
    } else if (totalIssues > 20) {
      findings.push(`⚠️ ${totalIssues} security issues found - your code has more holes than Swiss cheese! 🧀`)
    }

    // Repository count roasts
    if (repositories.length < 3) {
      findings.push(`📂 Only ${repositories.length} repos scanned - either you're new or you delete your mistakes! 🗑️`)
    }

    // Language diversity roasts
    const languages = [...new Set(repositories.map(repo => repo.repoInfo.language).filter(Boolean))]
    if (languages.length === 1) {
      findings.push(`🔧 Only coding in ${languages[0]} - afraid to leave your comfort zone? 😏`)
    }

    // Stars and engagement roasts
    const totalStars = repositories.reduce((sum, repo) => sum + repo.repoInfo.stars, 0)
    if (totalStars === 0) {
      findings.push(`⭐ Zero stars across all repos - even your mom didn't star your projects! 👩‍💻`)
    } else if (totalStars < 10) {
      findings.push(`⭐ ${totalStars} total stars - your code is less popular than a root canal! 🦷`)
    }

    return findings
  }

  private generateProfileInsults(userInfo: UserScanResult['userInfo'], repositories: RepoScanResult[]): string[] {
    const insults: string[] = []

    // Bio roasts
    if (!userInfo.bio) {
      insults.push(`📝 No bio - mysterious or just can't describe yourself in words? 🤐`)
    } else if (userInfo.bio.includes('Full Stack')) {
      insults.push(`📝 "Full Stack" in bio - translation: "I'm mediocre at everything!" 🥞`)
    }

    // Location roasts
    if (!userInfo.location) {
      insults.push(`📍 No location set - hiding from your code's victims? 🕵️`)
    }

    // Follower ratio roasts
    if (userInfo.followers === 0) {
      insults.push(`👥 Zero followers - even bots don't want to follow you! 🤖`)
    } else if (userInfo.following > userInfo.followers * 3) {
      insults.push(`👥 Following ${userInfo.following} but only ${userInfo.followers} follow back - desperate much? 😅`)
    }

    // Join date roasts
    const joinYear = new Date(userInfo.joinedDate).getFullYear()
    const currentYear = new Date().getFullYear()
    if (currentYear - joinYear > 5 && repositories.length < 10) {
      insults.push(`📅 ${currentYear - joinYear} years on GitHub with ${repositories.length} repos - what have you been doing? 🐌`)
    }

    return insults
  }

  private generateTypoRoasts(repositories: RepoScanResult[], typoStats: UserScanResult['typoStats']): string[] {
    const roasts: string[] = []

    if (typoStats.totalTypos === 0) {
      roasts.push(`✍️ Zero typos found - either you're perfect or you don't write documentation! 📚`)
      return roasts
    }

    // Overall typo roasts
    if (typoStats.totalTypos > 20) {
      roasts.push(`📝 ${typoStats.totalTypos} typos across your repos - did you learn English from autocorrect? 🤖`)
    } else if (typoStats.totalTypos > 10) {
      roasts.push(`📝 ${typoStats.totalTypos} typos found - your keyboard has trust issues with you! ⌨️`)
    } else if (typoStats.totalTypos > 5) {
      roasts.push(`📝 ${typoStats.totalTypos} typos detected - spell check is your friend! 🔤`)
    }

    // Embarrassment level roasts
    switch (typoStats.embarrassmentLevel) {
      case 'cringe':
        roasts.push(`😬 Embarrassment level: CRINGE - your typos have typos! 🤦‍♂️`)
        break
      case 'awkward':
        roasts.push(`😅 Embarrassment level: AWKWARD - even autocorrect gave up on you! 📱`)
        break
      case 'noticeable':
        roasts.push(`👀 Embarrassment level: NOTICEABLE - your typos are more visible than your code! 🔍`)
        break
      case 'minor':
        roasts.push(`🤏 Embarrassment level: MINOR - just a few typos, we've all been there! ✏️`)
        break
    }

    // Worst typo repo roast
    if (typoStats.worstTypoRepo) {
      const worstRepo = repositories.find(repo => repo.name === typoStats.worstTypoRepo)
      if (worstRepo && worstRepo.typoScan.totalTypos > 0) {
        roasts.push(`🏆 "${typoStats.worstTypoRepo}" wins the typo championship with ${worstRepo.typoScan.totalTypos} mistakes! 🎖️`)
      }
    }

    // Specific typo type roasts
    const repoTypos = repositories.flatMap(repo => repo.typoScan.issues)
    const repoNameTypos = repoTypos.filter(typo => typo.type === 'repo_name')
    const readmeTypos = repoTypos.filter(typo => typo.type === 'readme')
    const commitTypos = repoTypos.filter(typo => typo.type === 'commit_message')

    if (repoNameTypos.length > 0) {
      roasts.push(`📂 ${repoNameTypos.length} typos in repository names - first impressions matter! 👀`)
    }

    if (readmeTypos.length > 3) {
      roasts.push(`📖 ${readmeTypos.length} typos in README files - your documentation needs documentation! 📚`)
    }

    if (commitTypos.length > 5) {
      roasts.push(`💬 ${commitTypos.length} typos in commit messages - your git history is a spelling disaster! 📜`)
    }

    return roasts
  }

  private personalizeUserComment(baseComment: string, userInfo: UserScanResult['userInfo'], repositories: RepoScanResult[], worstRepo: RepoScanResult | null): string {
    if (worstRepo) {
      baseComment += ` Your worst offender "${worstRepo.name}" scored ${worstRepo.score}/100 - it's a masterpiece of insecurity! 🎨💀`
    }

    if (repositories.length > 0) {
      const avgScore = repositories.reduce((sum, repo) => sum + repo.score, 0) / repositories.length
      baseComment += ` With an average DumbDev score of ${Math.round(avgScore)}, you're consistently... consistent! 📊`
    }

    return baseComment
  }

  private getUserRoastByScore(averageScore: number, totalIssues: number, repoCount: number) {
    // Adjust roast based on multiple factors
    const adjustedScore = averageScore + (totalIssues > 30 ? 10 : 0) + (repoCount < 3 ? 5 : 0)

    // Use the existing roast system but with user-specific messages
    return this.getRoastByScore(Math.min(adjustedScore, 100), [])
  }

  private personalizeComment(baseComment: string, issues: SecurityIssue[], repoInfo: any): string {
    // Add specific issue mentions to make the roast more personal
    const criticalIssues = issues.filter(i => i.severity === 'critical')
    const secretIssues = issues.filter(i => i.type === 'secret')
    
    if (criticalIssues.length > 0) {
      baseComment += ` You've got ${criticalIssues.length} CRITICAL issue(s) that would make hackers do a happy dance! 💃🕺`
    }
    
    if (secretIssues.length > 0) {
      baseComment += ` Your secrets are more exposed than a reality TV show! 📺🔓`
    }

    // Add language-specific roasts
    if (repoInfo.language) {
      const languageRoasts: Record<string, string> = {
        'JavaScript': ' Your JavaScript is so insecure, even Node.js is crying! 😭',
        'Python': ' Your Python code has more holes than Swiss cheese! 🧀',
        'Java': ' Your Java is so vulnerable, even Oracle is concerned! ☕',
        'TypeScript': ' TypeScript can\'t type-check your security issues! 📝',
        'Go': ' Your Go code should go back to security school! 🎓',
        'Rust': ' Even Rust can\'t save you from these memory-unsafe practices! 🦀',
        'PHP': ' PHP and security issues - name a more iconic duo! 💫',
      }
      
      if (languageRoasts[repoInfo.language]) {
        baseComment += languageRoasts[repoInfo.language]
      }
    }

    return baseComment
  }

  private getRoastByScore(score: number, issues: SecurityIssue[]) {
    // Critical security disasters (80-100)
    if (score >= 80) {
      const criticalRoasts = [
        {
          title: "💀 SECURITY APOCALYPSE 💀",
          comment: "HOLY MOLY! Your repo is more exposed than a nudist beach in Times Square! Did you just speedrun every security vulnerability known to humanity?",
          meme: "💀🔥💀🔥💀",
          emoji: "🚨",
        },
        {
          title: "🤡 HACKER'S PARADISE 🤡",
          comment: "Congratulations! You've created the perfect honeypot for hackers! Your security is so bad, even script kiddies are feeling sorry for you!",
          meme: "🤡🎪🤡🎪🤡",
          emoji: "🎪",
        },
        {
          title: "🔥 DUMPSTER FIRE DETECTED 🔥",
          comment: "Your code security is like a screen door on a submarine! At this point, you might as well just email your database to random strangers!",
          meme: "🔥🗑️🔥🗑️🔥",
          emoji: "🗑️",
        },
      ]
      return criticalRoasts[Math.floor(Math.random() * criticalRoasts.length)]
    }

    // High security issues (60-79)
    if (score >= 60) {
      const highRoasts = [
        {
          title: "🍕 HACKER'S PIZZA PARTY 🍕",
          comment: "Your security holes are so big, hackers are ordering pizza and making themselves at home! Did you forget that security is a thing?",
          meme: "🍕🏴‍☠️🍕🏴‍☠️🍕",
          emoji: "🏴‍☠️",
        },
        {
          title: "😬 YIKES ON BIKES 😬",
          comment: "You're giving me secondhand embarrassment! It's like watching someone trip in public, but in code form and way more dangerous!",
          meme: "😬🚴‍♂️💥😬🚴‍♂️",
          emoji: "🚴‍♂️",
        },
      ]
      return highRoasts[Math.floor(Math.random() * highRoasts.length)]
    }

    // Medium security issues (40-59)
    if (score >= 40) {
      const mediumRoasts = [
        {
          title: "🤦‍♂️ FACEPALM WORTHY 🤦‍♂️",
          comment: "Your security is like a chocolate teapot - looks nice but melts under pressure! At least you're consistent... consistently vulnerable!",
          meme: "🤦‍♂️🍫🤦‍♂️🍫🤦‍♂️",
          emoji: "🍫",
        },
        {
          title: "🎭 AMATEUR HOUR 🎭",
          comment: "Your security practices are giving me 'my first website' vibes! Did you learn cybersecurity from a cereal box?",
          meme: "🎭📚🎭📚🎭",
          emoji: "📚",
        },
      ]
      return mediumRoasts[Math.floor(Math.random() * mediumRoasts.length)]
    }

    // Low security issues (20-39)
    if (score >= 20) {
      const lowRoasts = [
        {
          title: "👏 NOT TERRIBLE! 👏",
          comment: "Look at you being somewhat responsible! Your security isn't completely hopeless - just mostly hopeless! Progress! 🎉",
          meme: "👏✨👏✨👏",
          emoji: "✨",
        },
        {
          title: "🎯 GETTING WARMER 🎯",
          comment: "You're like a security student who actually attended some classes! Not great, not terrible, just... existing in the middle!",
          meme: "🎯📖🎯📖🎯",
          emoji: "📖",
        },
      ]
      return lowRoasts[Math.floor(Math.random() * lowRoasts.length)]
    }

    // Good security (5-19)
    if (score >= 5) {
      const goodRoasts = [
        {
          title: "🔥 PRETTY SOLID! 🔥",
          comment: "Wow! Someone actually reads security documentation! Are you sure you're a real developer? This level of responsibility is suspicious! 🤔",
          meme: "🔥📚🔥📚🔥",
          emoji: "🕵️‍♂️",
        },
        {
          title: "🛡️ SECURITY CONSCIOUS 🛡️",
          comment: "Your security game is stronger than my coffee! You've got some minor issues, but overall you're not embarrassing yourself!",
          meme: "🛡️☕🛡️☕🛡️",
          emoji: "☕",
        },
      ]
      return goodRoasts[Math.floor(Math.random() * goodRoasts.length)]
    }

    // Excellent security (0-4)
    return {
      title: "👑 SECURITY LEGEND! 👑",
      comment: "Your code is more secure than my browser history! Teach me your ways, security sensei! Are you even human? 🤖✨",
      meme: "👑🛡️👑🛡️👑",
      emoji: "🥷",
    }
  }
}
