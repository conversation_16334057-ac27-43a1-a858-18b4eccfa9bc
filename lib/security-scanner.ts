import { Octokit } from '@octokit/rest'
import { TypoDetector, TypoScanResult } from './typo-detector'

export interface SecurityIssue {
  type: 'secret' | 'dependency' | 'config' | 'code' | 'auth'
  severity: 'critical' | 'high' | 'medium' | 'low'
  title: string
  description: string
  file?: string
  line?: number
  suggestion: string
}

export interface RepoScanResult {
  name: string
  score: number
  issues: SecurityIssue[]
  typoScan: TypoScanResult
  repoInfo: {
    name: string
    description: string
    language: string
    stars: number
    forks: number
    isPrivate: boolean
    url: string
  }
}

export interface UserScanResult {
  username: string
  totalScore: number
  averageScore: number
  totalIssues: number
  totalTypos: number
  repositories: RepoScanResult[]
  userInfo: {
    name: string
    bio: string
    location: string
    company: string
    publicRepos: number
    followers: number
    following: number
    avatarUrl: string
    profileUrl: string
    joinedDate: string
  }
  worstRepo: RepoScanResult | null
  bestRepo: RepoScanResult | null
  typoStats: {
    totalTypos: number
    embarrassmentLevel: string
    worstTypoRepo: string | null
  }
}

export class SecurityScanner {
  private octokit: Octokit
  private typoDetector: TypoDetector

  constructor(token?: string) {
    this.octokit = new Octokit({
      auth: token || process.env.GITHUB_TOKEN,
    })
    this.typoDetector = new TypoDetector()
  }

  async scanRepository(owner: string, repo: string): Promise<RepoScanResult> {
    try {
      // Get repository information
      const { data: repoData } = await this.octokit.repos.get({
        owner,
        repo,
      })

      const repoInfo = {
        name: repoData.name,
        description: repoData.description || '',
        language: repoData.language || 'Unknown',
        stars: repoData.stargazers_count,
        forks: repoData.forks_count,
        isPrivate: repoData.private,
        url: repoData.html_url,
      }

      // Get repository contents
      const issues: SecurityIssue[] = []
      
      // Scan for various security issues
      await Promise.all([
        this.scanForSecrets(owner, repo, issues),
        this.scanForConfigIssues(owner, repo, issues),
        this.scanForDependencyIssues(owner, repo, issues),
        this.scanForCodeIssues(owner, repo, issues),
      ])

      // Scan for typos
      const typoScan = await this.scanForTypos(owner, repo, repoData)

      // Calculate security score (0-100, lower is better for roasting)
      const score = this.calculateSecurityScore(issues)

      return {
        name: repoData.name,
        score,
        issues,
        typoScan,
        repoInfo,
      }
    } catch (error: any) {
      console.error('Error scanning repository:', error)

      if (error.status === 404) {
        throw new Error('Repository not found. Make sure the repository exists and is public.')
      }

      if (error.status === 403) {
        throw new Error('Access denied. The repository might be private or you\'ve hit the rate limit. Try adding a GitHub token.')
      }

      if (error.status === 401) {
        throw new Error('Authentication failed. Please check your GitHub token.')
      }

      throw new Error('Failed to scan repository. Please try again later.')
    }
  }

  async scanUser(username: string): Promise<UserScanResult> {
    try {
      // Get user information
      const { data: userData } = await this.octokit.users.getByUsername({
        username,
      })

      const userInfo = {
        name: userData.name || username,
        bio: userData.bio || '',
        location: userData.location || '',
        company: userData.company || '',
        publicRepos: userData.public_repos,
        followers: userData.followers,
        following: userData.following,
        avatarUrl: userData.avatar_url,
        profileUrl: userData.html_url,
        joinedDate: userData.created_at,
      }

      // Get user's repositories (limit to first 30 for performance)
      const { data: repos } = await this.octokit.repos.listForUser({
        username,
        type: 'all',
        sort: 'updated',
        per_page: 30,
      })

      // Filter out forks and focus on original repositories
      interface GitHubRepo {
        name: string
        fork: boolean
        [key: string]: any
      }
      const originalRepos: GitHubRepo[] = (repos as GitHubRepo[]).filter((repo: GitHubRepo) => !repo.fork)

      // Scan each repository (limit to 10 most recent for performance)
      const repoScans: RepoScanResult[] = []
      const reposToScan = originalRepos.slice(0, 10)

      for (const repo of reposToScan) {
        try {
          const scanResult = await this.scanRepository(username, repo.name)
          repoScans.push(scanResult)

          // Add small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 100))
        } catch (error) {
          console.warn(`Failed to scan repository ${repo.name}:`, error)
          // Continue with other repos even if one fails
        }
      }

      // Calculate overall statistics
      const totalIssues = repoScans.reduce((sum, repo) => sum + repo.issues.length, 0)
      const totalScore = repoScans.reduce((sum, repo) => sum + repo.score, 0)
      const averageScore = repoScans.length > 0 ? Math.round(totalScore / repoScans.length) : 0
      const totalTypos = repoScans.reduce((sum, repo) => sum + repo.typoScan.totalTypos, 0)

      // Find best and worst repositories
      const worstRepo = repoScans.length > 0
        ? repoScans.reduce((worst, current) => current.score > worst.score ? current : worst)
        : null

      const bestRepo = repoScans.length > 0
        ? repoScans.reduce((best, current) => current.score < best.score ? current : best)
        : null

      // Calculate typo statistics
      const worstTypoRepo = repoScans.length > 0
        ? repoScans.reduce((worst, current) =>
            current.typoScan.totalTypos > worst.typoScan.totalTypos ? current : worst
          )
        : null

      const overallEmbarrassmentLevel = this.calculateOverallEmbarrassmentLevel(repoScans)

      return {
        username,
        totalScore,
        averageScore,
        totalIssues,
        totalTypos,
        repositories: repoScans,
        userInfo,
        worstRepo,
        bestRepo,
        typoStats: {
          totalTypos,
          embarrassmentLevel: overallEmbarrassmentLevel,
          worstTypoRepo: worstTypoRepo?.name || null,
        },
      }
    } catch (error: any) {
      console.error('Error scanning user:', error)

      if (error.status === 404) {
        throw new Error('User not found. Make sure the username exists on GitHub.')
      }

      if (error.status === 403) {
        throw new Error('Access denied. You might have hit the rate limit. Try adding a GitHub token.')
      }

      throw new Error('Failed to scan user profile. Please try again later.')
    }
  }

  private async scanForSecrets(owner: string, repo: string, issues: SecurityIssue[]): Promise<void> {
    try {
      // Search for common secret patterns in code
      const secretPatterns = [
        { pattern: 'api[_-]?key', name: 'API Key' },
        { pattern: 'secret[_-]?key', name: 'Secret Key' },
        { pattern: 'password\\s*=', name: 'Hardcoded Password' },
        { pattern: 'token\\s*=', name: 'Token' },
        { pattern: 'aws[_-]?access[_-]?key', name: 'AWS Access Key' },
        { pattern: 'private[_-]?key', name: 'Private Key' },
      ]

      for (const { pattern, name } of secretPatterns) {
        try {
          const searchResults = await this.octokit.search.code({
            q: `${pattern} repo:${owner}/${repo}`,
            per_page: 5,
          })

          if (searchResults.data.total_count > 0) {
            issues.push({
              type: 'secret',
              severity: 'critical',
              title: `Potential ${name} Exposure`,
              description: `Found ${searchResults.data.total_count} potential ${name.toLowerCase()} exposures in your code.`,
              suggestion: `Remove hardcoded secrets and use environment variables or secret management services.`,
            })
          }
        } catch (error) {
          // GitHub search API can be rate limited, continue with other checks
          console.warn(`Search failed for pattern ${pattern}:`, error)
        }
      }
    } catch (error) {
      console.warn('Secret scanning failed:', error)
    }
  }

  private async scanForConfigIssues(owner: string, repo: string, issues: SecurityIssue[]): Promise<void> {
    try {
      // Check for .gitignore
      try {
        await this.octokit.repos.getContent({
          owner,
          repo,
          path: '.gitignore',
        })
      } catch (error) {
        issues.push({
          type: 'config',
          severity: 'high',
          title: 'Missing .gitignore File',
          description: 'No .gitignore file found. This could lead to accidentally committing sensitive files.',
          suggestion: 'Add a comprehensive .gitignore file for your project type.',
        })
      }

      // Check for environment files in commits
      try {
        const envFiles = ['.env', '.env.local', '.env.production', 'config.json']
        for (const file of envFiles) {
          try {
            await this.octokit.repos.getContent({
              owner,
              repo,
              path: file,
            })
            issues.push({
              type: 'config',
              severity: 'critical',
              title: `Environment File Committed: ${file}`,
              description: `Found ${file} in repository. This likely contains sensitive configuration.`,
              file,
              suggestion: `Remove ${file} from git history and add it to .gitignore.`,
            })
          } catch {
            // File doesn't exist, which is good
          }
        }
      } catch (error) {
        console.warn('Config file check failed:', error)
      }
    } catch (error) {
      console.warn('Config scanning failed:', error)
    }
  }

  private async scanForDependencyIssues(owner: string, repo: string, issues: SecurityIssue[]): Promise<void> {
    try {
      // Check for package.json (Node.js)
      try {
        const { data: packageJson } = await this.octokit.repos.getContent({
          owner,
          repo,
          path: 'package.json',
        })

        if ('content' in packageJson) {
          const content = JSON.parse(Buffer.from(packageJson.content, 'base64').toString())
          
          // Check for outdated or vulnerable packages (simplified check)
          const vulnerablePackages = [
            'lodash', 'moment', 'request', 'node-uuid', 'crypto-js'
          ]

          const dependencies = { ...content.dependencies, ...content.devDependencies }
          for (const pkg of vulnerablePackages) {
            if (dependencies[pkg]) {
              issues.push({
                type: 'dependency',
                severity: 'medium',
                title: `Potentially Vulnerable Dependency: ${pkg}`,
                description: `Using ${pkg} which may have known security issues.`,
                file: 'package.json',
                suggestion: `Review and update ${pkg} to the latest secure version.`,
              })
            }
          }
        }
      } catch {
        // No package.json found
      }

      // Check for requirements.txt (Python)
      try {
        const { data: requirements } = await this.octokit.repos.getContent({
          owner,
          repo,
          path: 'requirements.txt',
        })

        if ('content' in requirements) {
          const content = Buffer.from(requirements.content, 'base64').toString()
          if (content.includes('==') && !content.includes('>=')) {
            issues.push({
              type: 'dependency',
              severity: 'medium',
              title: 'Pinned Dependencies Without Updates',
              description: 'Dependencies are pinned to exact versions without allowing security updates.',
              file: 'requirements.txt',
              suggestion: 'Use version ranges (>=) to allow security updates.',
            })
          }
        }
      } catch {
        // No requirements.txt found
      }
    } catch (error) {
      console.warn('Dependency scanning failed:', error)
    }
  }

  private async scanForCodeIssues(owner: string, repo: string, issues: SecurityIssue[]): Promise<void> {
    try {
      // Search for common code security issues
      const codeIssues = [
        { pattern: 'eval\\(', name: 'eval() Usage', severity: 'high' as const },
        { pattern: 'innerHTML\\s*=', name: 'innerHTML Assignment', severity: 'medium' as const },
        { pattern: 'document\\.write', name: 'document.write Usage', severity: 'medium' as const },
        { pattern: 'console\\.log', name: 'Console.log Statements', severity: 'low' as const },
        { pattern: 'TODO|FIXME|HACK', name: 'TODO/FIXME Comments', severity: 'low' as const },
      ]

      for (const { pattern, name, severity } of codeIssues) {
        try {
          const searchResults = await this.octokit.search.code({
            q: `${pattern} repo:${owner}/${repo}`,
            per_page: 5,
          })

          if (searchResults.data.total_count > 0) {
            issues.push({
              type: 'code',
              severity,
              title: `${name} Found`,
              description: `Found ${searchResults.data.total_count} instances of ${name.toLowerCase()} in your code.`,
              suggestion: this.getCodeIssueSuggestion(name),
            })
          }
        } catch (error) {
          console.warn(`Code search failed for pattern ${pattern}:`, error)
        }
      }
    } catch (error) {
      console.warn('Code scanning failed:', error)
    }
  }

  private getCodeIssueSuggestion(issueName: string): string {
    const suggestions: Record<string, string> = {
      'eval() Usage': 'Avoid eval() as it can execute arbitrary code. Use safer alternatives.',
      'innerHTML Assignment': 'Use textContent or sanitize HTML to prevent XSS attacks.',
      'document.write Usage': 'Use modern DOM manipulation methods instead of document.write.',
      'Console.log Statements': 'Remove console.log statements before production deployment.',
      'TODO/FIXME Comments': 'Address TODO and FIXME comments before releasing.',
    }
    return suggestions[issueName] || 'Review and fix this issue.'
  }

  private async scanForTypos(owner: string, repo: string, repoData: any): Promise<TypoScanResult> {
    try {
      let readmeContent = ''
      let commitMessages: string[] = []

      // Try to get README content
      try {
        const readmeFiles = ['README.md', 'README.txt', 'README.rst', 'README']
        for (const filename of readmeFiles) {
          try {
            const { data: readme } = await this.octokit.repos.getContent({
              owner,
              repo,
              path: filename,
            })
            if ('content' in readme) {
              readmeContent = Buffer.from(readme.content, 'base64').toString()
              break
            }
          } catch {
            // Try next README file
          }
        }
      } catch (error) {
        console.warn('Failed to get README:', error)
      }

      // Get recent commit messages
      try {
        const { data: commits } = await this.octokit.repos.listCommits({
          owner,
          repo,
          per_page: 10,
        })
        commitMessages = commits.map(commit => commit.commit.message)
      } catch (error) {
        console.warn('Failed to get commits:', error)
      }

      // Scan for typos
      return this.typoDetector.scanForTypos(
        repoData.name,
        repoData.description || '',
        readmeContent,
        commitMessages
      )
    } catch (error) {
      console.warn('Typo scanning failed:', error)
      return {
        totalTypos: 0,
        typosByType: {},
        issues: [],
        embarrassmentLevel: 'clean'
      }
    }
  }

  private calculateOverallEmbarrassmentLevel(repoScans: RepoScanResult[]): string {
    if (repoScans.length === 0) return 'clean'

    const totalTypos = repoScans.reduce((sum, repo) => sum + repo.typoScan.totalTypos, 0)
    const avgTyposPerRepo = totalTypos / repoScans.length

    if (avgTyposPerRepo >= 10) return 'cringe'
    if (avgTyposPerRepo >= 5) return 'awkward'
    if (avgTyposPerRepo >= 2) return 'noticeable'
    if (avgTyposPerRepo >= 1) return 'minor'
    return 'clean'
  }

  private calculateSecurityScore(issues: SecurityIssue[]): number {
    let score = 0

    for (const issue of issues) {
      switch (issue.severity) {
        case 'critical':
          score += 25
          break
        case 'high':
          score += 15
          break
        case 'medium':
          score += 8
          break
        case 'low':
          score += 3
          break
      }
    }

    // Cap at 100
    return Math.min(score, 100)
  }
}

export function parseGitHubUrl(url: string): { owner: string; repo: string } | null {
  const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/)
  if (!match) return null

  return {
    owner: match[1],
    repo: match[2].replace(/\.git$/, ''),
  }
}

export function parseGitHubUser(input: string): string | null {
  // Handle GitHub profile URLs
  const urlMatch = input.match(/github\.com\/([^\/]+)\/?$/)
  if (urlMatch) {
    return urlMatch[1]
  }

  // Handle plain usernames (validate GitHub username format)
  const usernameMatch = input.match(/^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?$/)
  if (usernameMatch && input.length <= 39) {
    return input
  }

  return null
}
