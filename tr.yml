name: MeTTaLog CI

on:
  push:
    branches: [ref/mettalog]
  pull_request:
    branches: [ref/mettalog]

jobs:
  mettalog-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Python 3.12
        uses: actions/setup-python@v4
        with:
          python-version: '3.12.3'

      - name: Upgrade pip and install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install numpy pyswip janus ansi2html junit2html

      - name: Install SWI-Prolog from PPA
        run: |
          sudo apt-get remove -y swi-prolog* || true
          sudo apt-get update
          sudo apt-add-repository ppa:swi-prolog/stable -y
          sudo apt-get update
          sudo apt-get install -y swi-prolog

      - name: Install MeTTaLog (runs INSTALL.sh)
        run: |
          # 1) Clone into $HOME so INSTALL.sh installs here
          git clone https://github.com/trueagi-io/metta-wam.git --depth 1 $HOME/metta-wam
          cd $HOME/metta-wam

          # 2) Run the installer
          chmod +x ./INSTALL.sh
          bash -c "source ./INSTALL.sh --allow-system-modifications"

          # 3) Add the real install location to PATH
          echo "/home/<USER>/metta-wam"       >> $GITHUB_PATH
          echo "/home/<USER>/metta-wam/venv/bin" >> $GITHUB_PATH

          # 4) Preserve any other needed environment variables
          echo "SWIPL_BOOT_FLAGS=--no-pce"        >> $GITHUB_ENV
          echo "METTALOG_DIR=/home/<USER>/metta-wam" >> $GITHUB_ENV

      - name: Debug mettalog installation
        run: |
          echo "PATH is: $PATH"
          which mettalog
          ls -la /home/<USER>/metta-wam/libraries

      - name: Run MeTTaLog Tests
        run: |
          which mettalog
          python3 scripts/run-tests.py
