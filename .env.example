# GitHub API Configuration
# Get your token from: https://github.com/settings/tokens
# Only needs public repository access for public repos
# GITHUB_TOKEN=your_github_token_here

# Optional: For higher rate limits, you can use a GitHub App
# GITHUB_APP_ID=your_app_id
# GITHUB_APP_PRIVATE_KEY=your_private_key

# Note: The scanner works without a token but has lower rate limits
# For testing, you can leave GITHUB_TOKE<PERSON> commented out
