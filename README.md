# 💀 Dumb Dev Scanner 💀

A hilarious security scanner that roasts your GitHub repositories while actually finding real security issues! 🔥

## 🚀 Features

- **Real GitHub API Integration**: Actually scans your public repositories
- **Security Issue Detection**: Finds common security vulnerabilities including:
  - 🔑 Exposed API keys and secrets
  - 📁 Committed environment files
  - 🙈 Missing .gitignore files
  - 📦 Vulnerable dependencies
  - 💻 Insecure code patterns
- **Hilarious Roasts**: Get roasted based on your actual security score
- **Detailed Findings**: See exactly what issues were found and how to fix them
- **Shareable Results**: Share your shame (or pride) on social media

## 🛠️ Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd dumb-dev-scanner
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up GitHub API access** (Optional but recommended)
   - Create a GitHub Personal Access Token at https://github.com/settings/tokens
   - Copy `.env.local` and add your token:
   ```bash
   GITHUB_TOKEN=your_github_token_here
   ```
   
   > **Note**: The scanner works without a token but has lower rate limits

4. **Run the development server**
   ```bash
   pnpm dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000` (or the port shown in terminal)

## 🎯 How to Use

1. Enter any public GitHub repository URL
2. Click "SCAN & ROAST THIS REPO!"
3. Watch the hilarious scanning animation
4. Get absolutely roasted based on your security score
5. Review the specific security issues found
6. Fix the issues and scan again for a better score!

## 🔒 Security Scanning

The scanner checks for:

### Critical Issues (25 points each)
- Exposed API keys, passwords, and secrets
- Committed environment files (.env, config.json)

### High Issues (15 points each)
- Missing .gitignore file
- eval() usage in JavaScript

### Medium Issues (8 points each)
- Vulnerable dependencies
- innerHTML assignments
- document.write usage

### Low Issues (3 points each)
- Console.log statements
- TODO/FIXME comments

## 🎭 Roast Levels

- **0-4**: 👑 Security Legend
- **5-19**: 🔥 Pretty Solid
- **20-39**: 👏 Not Terrible
- **40-59**: 🤦‍♂️ Amateur Hour
- **60-79**: 🍕 Hacker's Pizza Party
- **80-100**: 💀 Security Apocalypse

## 🤝 Contributing

Want to add more security checks or funnier roasts? PRs welcome!

## ⚠️ Disclaimer

This tool scans public GitHub repositories for educational and entertainment purposes. No data is stored, and we only access publicly available information. Always follow responsible disclosure practices for any security issues you find.

## 🛠️ Tech Stack

- **Next.js 15** - React framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Radix UI** - UI components
- **Octokit** - GitHub API client
- **Lucide React** - Icons

---

Made with 💀 and lots of caffeine by developers who've been there 😭

Remember: We roast because we care! Now go fix those security issues! 🛠️💀
