"use client"

import React from 'react'
import { cn } from '@/lib/utils'

interface CircularProgressProps {
  value: number
  size?: number
  strokeWidth?: number
  className?: string
  children?: React.ReactNode
}

export function CircularProgress({
  value,
  size = 200,
  strokeWidth = 8,
  className,
  children,
}: CircularProgressProps) {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (value / 100) * circumference

  // Color based on score (lower is better for security)
  const getColor = (score: number) => {
    if (score >= 80) return '#ef4444' // red - critical
    if (score >= 60) return '#f97316' // orange - high
    if (score >= 40) return '#eab308' // yellow - medium
    if (score >= 20) return '#22d3ee' // cyan - low
    return '#10b981' // green - excellent
  }

  const getGradient = (score: number) => {
    if (score >= 80) return 'from-red-500 to-red-600'
    if (score >= 60) return 'from-orange-500 to-red-500'
    if (score >= 40) return 'from-yellow-500 to-orange-500'
    if (score >= 20) return 'from-cyan-500 to-yellow-500'
    return 'from-green-500 to-cyan-500'
  }

  const strokeColor = getColor(value)
  const gradientClass = getGradient(value)

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
        viewBox={`0 0 ${size} ${size}`}
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-slate-700/30"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-1000 ease-out"
          style={{
            filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.3))',
          }}
        />
      </svg>
      
      {/* Content in the center */}
      <div className="absolute inset-0 flex items-center justify-center">
        {children || (
          <div className="text-center">
            <div 
              className={`text-4xl font-black bg-gradient-to-br ${gradientClass} bg-clip-text text-transparent`}
              style={{ fontFamily: "Comic Sans MS, cursive" }}
            >
              {value}
            </div>
            <div className="text-sm text-slate-400 font-bold">DUMB SCORE</div>
          </div>
        )}
      </div>
    </div>
  )
}

interface DumbDevScoreProps {
  score: number
  size?: number
  className?: string
  showLabel?: boolean
}

export function DumbDevScore({ 
  score, 
  size = 200, 
  className,
  showLabel = true 
}: DumbDevScoreProps) {
  const getScoreLabel = (score: number) => {
    if (score >= 80) return { label: "DISASTER", emoji: "💀", color: "text-red-400" }
    if (score >= 60) return { label: "TERRIBLE", emoji: "🤡", color: "text-orange-400" }
    if (score >= 40) return { label: "BAD", emoji: "😬", color: "text-yellow-400" }
    if (score >= 20) return { label: "OKAY", emoji: "👏", color: "text-cyan-400" }
    return { label: "LEGEND", emoji: "👑", color: "text-green-400" }
  }

  const scoreInfo = getScoreLabel(score)

  return (
    <div className={cn("flex flex-col items-center space-y-4", className)}>
      <CircularProgress value={score} size={size}>
        <div className="text-center">
          <div 
            className="text-5xl font-black text-white mb-1"
            style={{ fontFamily: "Comic Sans MS, cursive" }}
          >
            {score}
          </div>
          <div className="text-xs text-slate-400 font-bold tracking-wider">
            DUMB SCORE
          </div>
        </div>
      </CircularProgress>
      
      {showLabel && (
        <div className="text-center">
          <div className="text-3xl mb-1">{scoreInfo.emoji}</div>
          <div 
            className={`text-xl font-black ${scoreInfo.color}`}
            style={{ fontFamily: "Comic Sans MS, cursive" }}
          >
            {scoreInfo.label}
          </div>
        </div>
      )}
    </div>
  )
}
